user www-data;
worker_processes auto;
pid /run/nginx.pid;
error_log /var/log/nginx/error.log;

events {
  worker_connections 1024;
}

http {
  client_max_body_size 100M;

  server {
    listen 80;
    server_name localhost;

    location / {
      proxy_pass http://localhost:3000;
      proxy_http_version 1.1; # Required for WebSocket
      proxy_set_header Upgrade $http_upgrade; # WebSocket header
      proxy_set_header Connection "upgrade"; # WebSocket header
      proxy_set_header Host $host;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
    }

    location /api/v1/ {
      proxy_pass http://localhost:8000;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
    }

    # MCP
    location /mcp/ {
      proxy_pass http://localhost:8001/mcp/;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /mcp {
      proxy_pass http://localhost:8001/mcp;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
    }


    location /docs {
      proxy_pass http://localhost:8000/docs;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
    }

    location /openapi.json {
      proxy_pass http://localhost:8000/openapi.json;
      proxy_read_timeout 30m;
      proxy_connect_timeout 30m;
    }

    # Static
    location /static {
      alias /app/servers/fastapi/static/;
      expires 1y;
      add_header Cache-Control "public, immutable";
    }

    location /app_data/images/ {
      alias /app_data/images/;
      expires 1y;
      add_header Cache-Control "public, immutable";
    }

    location /app_data/exports/ {
      alias /app_data/exports/;
      expires 1y;
      add_header Cache-Control "public, immutable";
    }

    location /app_data/uploads/ {
      alias /app_data/uploads/;
      expires 1y;
      add_header Cache-Control "public, immutable";
    }

    location /app_data/fonts/ {
      alias /app_data/fonts/;
      expires 1y;
      add_header Cache-Control "public, immutable";
    }
  }
}