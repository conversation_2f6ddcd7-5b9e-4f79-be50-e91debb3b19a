#!/bin/bash

# Build script for <PERSON>on with <PERSON><PERSON>
# This script builds the Presenton container image with proper Chrome/Puppeteer support

set -e

echo "🚀 Building Presenton with <PERSON><PERSON>..."

# Check if podman is installed
if ! command -v podman &> /dev/null; then
    echo "❌ Podman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Detect architecture
ARCH=$(uname -m)
if [[ "$ARCH" == "arm64" ]]; then
    echo "🍎 Detected Apple Silicon (ARM64) - building for linux/amd64 platform"
    PLATFORM_FLAG="--platform=linux/amd64"
else
    echo "💻 Detected x86_64 architecture"
    PLATFORM_FLAG=""
fi

# Build the production image
echo "📦 Building production image..."
podman build $PLATFORM_FLAG -t presenton:latest -f Dockerfile .

echo "✅ Build completed successfully!"
echo ""
echo "🚀 Now run the container with:"
echo "./run-podman.sh"
echo ""
echo "🌐 Then access the application at: http://localhost:5005"
