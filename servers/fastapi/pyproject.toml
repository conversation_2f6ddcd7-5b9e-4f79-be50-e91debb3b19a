[project]
name = "presenton-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11,<3.12"
dependencies = [
    "aiohttp>=3.12.15",
    "aiomysql>=0.2.0",
    "aiosqlite>=0.21.0",
    "anthropic>=0.60.0",
    "asyncpg>=0.30.0",
    "chromadb>=1.0.15",
    "docling>=2.43.0",
    "fastapi[standard]>=0.116.1",
    "fastmcp>=2.11.0",
    "google-genai>=1.28.0",
    "nltk>=3.9.1",
    "openai>=1.98.0",
    "pathvalidate>=3.3.1",
    "pdfplumber>=0.11.7",
    "pytest>=8.4.1",
    "python-pptx>=1.0.2",
    "redis>=6.2.0",
    "sqlmodel>=0.0.24",
]

[[tool.uv.index]]
url = "https://download.pytorch.org/whl/cpu"
