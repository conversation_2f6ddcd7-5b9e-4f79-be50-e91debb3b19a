#!/bin/bash

# Run script for Presenton with <PERSON><PERSON>
# This script runs the Presenton container with proper platform settings

set -e

echo "🚀 Starting Presenton with <PERSON><PERSON>..."

# Check if podman is installed
if ! command -v podman &> /dev/null; then
    echo "❌ <PERSON>dman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Stop and remove existing container if it exists
if podman ps -a --format "{{.Names}}" | grep -q "^presenton$"; then
    echo "🛑 Stopping existing container..."
    podman stop presenton || true
    podman rm presenton || true
fi

# Create app_data directory if it doesn't exist
mkdir -p ./app_data

# Load .env if present
if [ -f .env ]; then
    echo "📄 Loading environment from .env"
    # shellcheck disable=SC2046
    export $(grep -v '^#' .env | xargs -0 2>/dev/null || grep -v '^#' .env | xargs)
fi

HOST_PORT=${HOST_PORT:-5005}
LLM=${LLM:-openai}
IMAGE_PROVIDER=${IMAGE_PROVIDER:-dall-e-3}
CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS:-true}

# OPENAI_API_KEY intentionally left blank by default for security
OPENAI_API_KEY=${OPENAI_API_KEY:-}

if [[ -z "$OPENAI_API_KEY" ]]; then
    echo "⚠️  Warning: OPENAI_API_KEY is not set. You'll need to configure it in the UI."
    echo "   Or set it as an environment variable: export OPENAI_API_KEY='your-key'"
fi

ARCH=$(uname -m)
PLATFORM_FLAG=""
if [[ "$ARCH" == "arm64" || "$ARCH" == "aarch64" ]]; then
    PLATFORM_FLAG="--platform=linux/amd64"
fi

echo "🐳 Starting Presenton container on port ${HOST_PORT}..."
podman run -it -d \
    --name presenton \
    $PLATFORM_FLAG \
    --shm-size=1gb \
    -p ${HOST_PORT}:80 \
    -e LLM="$LLM" \
    -e OPENAI_API_KEY="$OPENAI_API_KEY" \
    -e IMAGE_PROVIDER="$IMAGE_PROVIDER" \
    -e CAN_CHANGE_KEYS="$CAN_CHANGE_KEYS" \
    -v "./app_data:/app_data" \
    presenton:latest

echo "✅ Container started successfully!"
echo ""
echo "🌐 Access the application at: http://localhost:${HOST_PORT}"
echo "📋 Container name: presenton"
echo ""
echo "📊 To view logs: podman logs -f presenton"
echo "🛑 To stop: podman stop presenton"
echo "🗑️  To remove: podman rm presenton"
