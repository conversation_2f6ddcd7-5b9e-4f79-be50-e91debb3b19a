services:
  production:
    # image: ghcr.io/presenton/presenton:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Set to 'true' to install Ollama inside the image (may fail behind strict proxies)
        INSTALL_OLLAMA: "false"
    ports:
      # You can replace 5005 with any other port number of your choice to run Presenton on a different port number.
      - "5005:80"
    volumes:
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_MODEL=${GOOGLE_MODEL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ANTHROPIC_MODEL=${ANTHROPIC_MODEL}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - EXTENDED_REASONING=${EXTENDED_REASONING}
      - TOOL_CALLS=${TOOL_CALLS}
      - DISABLE_THINKING=${DISABLE_THINKING}
      - WEB_GROUNDING=${WEB_GROUNDING}
      - DATABASE_URL=${DATABASE_URL}
      - DISABLE_ANONYMOUS_TRACKING=${DISABLE_ANONYMOUS_TRACKING}

  production-gpu:
    # image: ghcr.io/presenton/presenton:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        INSTALL_OLLAMA: "false"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    ports:
      # You can replace 5005 with any other port number of your choice to run Presenton on a different port number.
      - "5005:80"
    volumes:
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_MODEL=${GOOGLE_MODEL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ANTHROPIC_MODEL=${ANTHROPIC_MODEL}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - EXTENDED_REASONING=${EXTENDED_REASONING}
      - TOOL_CALLS=${TOOL_CALLS}
      - DISABLE_THINKING=${DISABLE_THINKING}
      - WEB_GROUNDING=${WEB_GROUNDING}
      - DATABASE_URL=${DATABASE_URL}
      - DISABLE_ANONYMOUS_TRACKING=${DISABLE_ANONYMOUS_TRACKING}

  development:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        INSTALL_OLLAMA: "false"
    ports:
      - "5005:80"
    volumes:
      - .:/app
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_MODEL=${GOOGLE_MODEL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ANTHROPIC_MODEL=${ANTHROPIC_MODEL}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - EXTENDED_REASONING=${EXTENDED_REASONING}
      - TOOL_CALLS=${TOOL_CALLS}
      - DISABLE_THINKING=${DISABLE_THINKING}
      - WEB_GROUNDING=${WEB_GROUNDING}
      - DATABASE_URL=${DATABASE_URL}
      - DISABLE_ANONYMOUS_TRACKING=${DISABLE_ANONYMOUS_TRACKING}

  development-gpu:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        INSTALL_OLLAMA: "false"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    ports:
      - "5005:80"
    volumes:
      - .:/app
      - ./app_data:/app_data
    environment:
      - CAN_CHANGE_KEYS=${CAN_CHANGE_KEYS}
      - LLM=${LLM}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_MODEL=${GOOGLE_MODEL}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - ANTHROPIC_MODEL=${ANTHROPIC_MODEL}
      - OLLAMA_URL=${OLLAMA_URL}
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - CUSTOM_LLM_URL=${CUSTOM_LLM_URL}
      - CUSTOM_LLM_API_KEY=${CUSTOM_LLM_API_KEY}
      - CUSTOM_MODEL=${CUSTOM_MODEL}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - EXTENDED_REASONING=${EXTENDED_REASONING}
      - TOOL_CALLS=${TOOL_CALLS}
      - DISABLE_THINKING=${DISABLE_THINKING}
      - WEB_GROUNDING=${WEB_GROUNDING}
      - DATABASE_URL=${DATABASE_URL}
      - DISABLE_ANONYMOUS_TRACKING=${DISABLE_ANONYMOUS_TRACKING}
